from datetime import datetime
from decimal import Decimal
from typing import Optional
from ninja import Schema
from pydantic import Field
from lca.accounting.schema import (
    CategorySchema,
    EmissionSourceSchema,
    GeographySchema,
    UnitSchema,
)
from lca.common.schema import UserSchema
from lca.database.models import (
    DATABASE_IMPORT_STATUS,
    EMISSION_SOURCE_PUBLISH_STATUS,
    EMISSION_SOURCE_STATUS,
    FLOW_TYPES,
    LICENSE_TYPES,
)
from lca.accounting.models import BOUNDARIES, OUTPUT_TYPES
from lca.file.schema import FileOut


class CN_EN(Schema):
    zh: str = Field(None, title="中文")
    en: str = Field(None, title="英文")

    # @model_validator(mode="after")
    # def check(cls, values):
    #     if not values.zh and not values.en:
    #         print(values)
    #         raise HttpError(422, "至少需要填写一个名称")
    #     return values


class FlowSchema(Schema):
    """
    流
    """

    id: int = Field(..., title="id")
    name: str = Field(..., title="名称")
    type: str = Field(..., title="流类型")


class EmissionSourceSimpleSchema(Schema):
    """
    因子简单
    """

    id: int = Field(..., title="id")
    name: str = Field(..., title="名称")


class EmissionInputSchema(Schema):
    """
    因子输入
    """

    amount: Decimal = Field(..., title="数量")
    flow: "Flow2Schema" = Field(..., title="流")
    unit: UnitSchema = Field(..., title="单位")
    related_emission_source: EmissionSourceSchema | None = Field(None, title="关联的排放源")
    description: str | None = Field(None, title="描述")


class EmissionInput(Schema):
    """
    因子输入
    """

    amount: Decimal = Field(..., title="数量")
    flow: FlowSchema = Field(..., title="流")
    unit: UnitSchema = Field(..., title="单位")
    related_emission_source: EmissionSourceSimpleSchema | None = Field(None, title="关联的排放源")
    description: str | None = Field(None, title="描述")


class EmissionOutputSchema(Schema):
    """
    因子输出
    """

    amount: Decimal = Field(..., title="数量")
    flow: "Flow2Schema" = Field(..., title="流")
    unit: UnitSchema = Field(..., title="单位")
    description: str | None = Field(None, title="描述")


class EmissionSourceDetailSchema(EmissionSourceSchema):
    """
    排放源
    """

    dataset: Optional["EmissionSourceDatasetSchema"] = Field(None, title="数据集")
    management: Optional["EmissionSourceManagementSchema"] = Field(None, title="数据管理")
    inputs: list["EmissionInputSchema"] = Field(..., title="输入")
    outputs: list[EmissionOutputSchema] = Field(..., title="输出")


class Flow2Schema(Schema):
    id: int = Field(..., title="id")
    name: CN_EN = Field(..., title="名称")
    type: str = Field(..., title="流类型")


class EmissionSourceDetailAdminSchema(EmissionSourceDetailSchema):
    reason: Optional[str] = Field(None, title="拒绝原因")


class EmissionSourceRejectInSchema(Schema):
    reason: str = Field(..., title="拒绝原因")


class EmissionSourceInputInSchema(Schema):
    flow_id: int = Field(..., title="流")
    flow_type: FLOW_TYPES = Field(..., title="流类型")
    amount: float = Field(..., title="数量")
    unit_id: str = Field(..., title="单位")
    related_emission_source_id: Optional[int] = Field(None, title="关联的排放源")


class EmissionSourceOutputInSchema(Schema):
    """
    因子输出
    """

    amount: float = Field(..., title="数量")
    unit_id: str = Field(..., title="单位")
    flow_id: int = Field(..., title="流")
    flow_type: FLOW_TYPES = Field(..., title="流类型")
    type: OUTPUT_TYPES = Field(..., title="输出类型")
    allocation: float = Field(..., title="分配占比")


class EmissionSourceInputOutputInSchema(Schema):
    inputs: list[EmissionSourceInputInSchema] = Field(..., title="输入")
    outputs: list[EmissionSourceOutputInSchema] = Field(..., title="输出")
    name: CN_EN = Field(..., title="过程名称")
    main_product_name: CN_EN = Field(..., title="主产品名称")
    unit_id: str = Field(..., title="单位")
    co2e: float = Field(..., title="CO2e")


class FlowInSchema(Schema):
    name: CN_EN = Field(..., title="名称")
    type: FLOW_TYPES = Field(..., title="流类型")


class EmissionSourceDatasetMixin:
    name: CN_EN = Field(..., title="名称")
    alias: Optional[CN_EN] = Field(None, title="别名")
    amount: float = Field(..., title="数量")
    unit_id: str = Field(..., title="单位")
    year: str = Field(..., title="基准年")
    geography_id: str = Field(..., title="地理代表性")
    specs: str = Field(..., title="产品型号")
    boundary: BOUNDARIES = Field(..., title="系统边界")
    functional_unit: CN_EN = Field(..., title="功能单位")
    category_id: str = Field(..., title="类别")
    source: CN_EN = Field(..., title="数据来源")
    usage: Optional[CN_EN] = Field(None, title="产品或工艺用途")
    technical_description: Optional[CN_EN] = Field(None, title="技术描述")
    allocation_principles: Optional[CN_EN] = Field(None, title="分配原则")
    flow_chart_id: str = Field(..., title="工艺流程图")
    model_description: Optional[CN_EN] = Field(None, title="模型描述")
    data_treatment: Optional[CN_EN] = Field(None, title="数据处理")


class EmissionSourceDatasetInSchema(Schema, EmissionSourceDatasetMixin):
    pass


class EmissionSourceDatasetSchema(Schema, EmissionSourceDatasetMixin):
    id: int = Field(..., title="id")
    category: CategorySchema = Field(..., title="类别")
    geography: GeographySchema = Field(..., title="地理代表性")
    unit: UnitSchema = Field(..., title="单位")
    flow_chart: Optional[FileOut] = Field(None, title="工艺流程图")
    pass


class EmissionSourceInSchema(Schema):
    dataset: EmissionSourceDatasetInSchema = Field(..., title="数据集文档")
    management: "EmissionSourceManagementInSchema" = Field(..., title="管理信息")
    inputs_outputs: EmissionSourceInputOutputInSchema = Field(..., title="输入输出表")


class EmissionSourceManagementMixin:
    generate_version: str = Field(..., title="数据生成版本")
    generate_contact: Optional[CN_EN] = Field(None, title="数据生成联系人")
    generate_contact_detail: Optional[str] = Field(None, title="数据生成联系方式")
    generate_create_time: Optional[datetime] = Field(None, title="数据生成时间")
    generate_update_time: Optional[datetime] = Field(None, title="数据生成更新时间")
    inputer_contact: Optional[CN_EN] | None = Field(None, title="数据录入者")
    input_create_time: Optional[datetime] = Field(None, title="数据录入时间")
    input_contact_detail: Optional[str] = Field(None, title="数据录入联系方式")
    update_major_count: Optional[int] = Field(None, title="大修次数")
    update_minor_count: Optional[int] = Field(None, title="小修次数")
    publish: Optional[EMISSION_SOURCE_PUBLISH_STATUS] = Field(None, title="发布信息")
    view: Optional[CN_EN] = Field(None, title="访问权限信息")
    owener: Optional[CN_EN] = Field(None, title="数据拥有者")
    owener_version: Optional[str] = Field(None, title="数据拥有者版本")
    license_type: Optional[LICENSE_TYPES] = Field(None, title="许可类型")
    copyright: CN_EN = Field(..., title="版权信息")


class EmissionSourceManagementInSchema(Schema, EmissionSourceManagementMixin):
    pass


class EmissionSourceManagementSchema(Schema, EmissionSourceManagementMixin):
    pass


class DatabaseSchema(Schema):
    id: int = Field(..., title="id")
    name: str = Field(..., title="名称")
    import_time: Optional[datetime] = Field(None, title="导入时间")
    create_time: datetime = Field(..., title="创建时间")
    update_time: datetime = Field(..., title="更新时间")


class DatabaseInSchema(Schema):
    name: str = Field(..., title="名称")
    file_id: str = Field(..., title="文件")


class DatabaseImportTaskSchema(Schema):
    id: int = Field(..., title="id")
    source: DatabaseSchema = Field(..., title="数据库源")
    import_time: datetime = Field(..., title="导入时间")
    progress: float = Field(0, title="导入进度")
    status: DATABASE_IMPORT_STATUS = Field(..., title="导入状态")
    create_time: datetime = Field(..., title="创建时间")
    update_time: datetime = Field(..., title="更新时间")


class EmissionSourceApplicationSchema(EmissionSourceDetailSchema):
    reason: Optional[str] = Field(None, title="拒绝原因")
    status: EMISSION_SOURCE_STATUS = Field(..., title="状态")
    approve_time: Optional[datetime] = Field(None, title="审核时间")
    approver: Optional[UserSchema] = Field(None, title="审核者")


class EmissionSourceApplicationRowSchema(EmissionSourceSchema):
    reason: Optional[str] = Field(None, title="拒绝原因")
    status: EMISSION_SOURCE_STATUS = Field(..., title="状态")
    approve_time: Optional[datetime] = Field(None, title="审核时间")
    approver: Optional[UserSchema] = Field(None, title="审核者")
