from datetime import datetime
import random
import uuid

from django.forms import model_to_dict
from lca.accounting.models import EmissionSource, Model
from lca.common.categoryServices import CategoryServices
from django.template.loader import render_to_string
from ninja.errors import HttpError
from lca.database.models import (
    EmissionSourceDataSet,
    EmissionSourceDataSetApplication,
    EmissionSourceInput,
    EmissionSourceInputApplication,
    EmissionSourceManagement,
    EmissionSourceManagementApplication,
    DATABASE_IMPORT_STATUS,
    EMISSION_SOURCE_STATUS,
    Database,
    DatabaseImportTask,
    EmissionSourceApplication,
    EmissionSourceOutput,
    EmissionSourceOutputApplication,
    Flow,
)
from lca.database.schema import EmissionSourceInSchema, FlowInSchema
from lca.file.models import File
from django.db import transaction

from lca.users.models import User


class DatabaseService:
    @staticmethod
    def search(text: str = "", category_id: str | None = None, **kwargs):
        query = EmissionSource.objects.filter(**kwargs)
        if category_id:
            ids = CategoryServices.get_all_id(category_id)
            query = query.filter(category_id__in=ids)
        if text:
            query = query.filter(name__icontains=text)

        return query.all()

    @staticmethod
    def export_process_ilcd(process: EmissionSource):
        start, end = DatabaseService.get_emission_source_start_end(process.dataset.year)
        cats = []
        cat = process.dataset.category
        while cat:
            cats.insert(0, cat)
            cat = cat.parent
        data = dict(
            process=process,
            dataset=process.dataset,
            management=process.management,
            inputs=process.inputs.all(),
            outputs=process.outputs.all(),
            start=start.strftime("%Y"),
            end=end.strftime("%Y"),
            start_time=int(start.timestamp()) * 1000,
            end_time=int(end.timestamp()) * 1000,
            management_time=process.management.generate_create_time.isoformat(),
            cats=cats,
            create_time=process.management.generate_create_time.isoformat(),
            last_update=process.management.generate_update_time.isoformat(),
        )
        return render_to_string("process.json", data)

    @staticmethod
    def get_emission_source_start_end(year: str):
        """转换年份的开始和结束时间"""
        times = year.split("-")
        start = datetime(int(times[0]), 1, 1)
        end = datetime(int(times[len(times) - 1]), 12, 31)
        return start, end

    @staticmethod
    def import_database(name, file: File):
        # 数据库中有同名的且不为导入失败的，不可以导入
        if Database.objects.filter(
            name=name,
            status__in=[
                DATABASE_IMPORT_STATUS.PENDING,
                DATABASE_IMPORT_STATUS.IN_PROGRESS,
                DATABASE_IMPORT_STATUS.SUCCESS,
            ],
        ).exists():
            raise HttpError(422, f"数据库中已存在同名数据{name}")
        database = Database.objects.create(name=name, file=file, status=DATABASE_IMPORT_STATUS.IN_PROGRESS)

        # 添加导入任务，进度随机0-99
        return DatabaseImportTask.objects.create(
            source=database,
            status=DATABASE_IMPORT_STATUS.IN_PROGRESS,
            import_time=datetime.now(),
            progress=random.randint(0, 99),
        )

    @staticmethod
    def export_database(database: Database):
        return database.file

    @staticmethod
    def create_emission_source_application(user: User, data: EmissionSourceInSchema):
        application = EmissionSourceApplication(
            creator=user,
            amount=1,
            uuid=uuid.uuid4(),
            status=EMISSION_SOURCE_STATUS.ONGOING,
        )
        return DatabaseService.update_emission_source_application(application, data)

    @staticmethod
    def update_emission_source_application(application: EmissionSourceApplication, data: EmissionSourceInSchema):
        with transaction.atomic():
            if application.id is not None:
                application = EmissionSourceApplication.objects.select_for_update().get(pk=application.id)
            if application.status != EMISSION_SOURCE_STATUS.ONGOING.value:
                raise HttpError(422, "当前状态不允许该操作")

            for key, value in data.model_dump(exclude=["dataset", "management", "inputs_outputs"]).items():
                setattr(application, key, value)
            application.name = data.inputs_outputs.name.dict()
            application.category_id = data.dataset.category_id
            application.geography_id = data.dataset.geography_id
            application.source = data.dataset.source.dict()
            application.year = data.dataset.year
            application.unit_id = data.inputs_outputs.unit_id
            application.co2e = data.inputs_outputs.co2e
            application.save()
            EmissionSourceManagementApplication.objects.update_or_create(
                emission_source=application, defaults=data.management.model_dump()
            )  # type: ignore
            EmissionSourceDataSetApplication.objects.update_or_create(
                emission_source=application, defaults=data.dataset.model_dump()
            )  # type: ignore
            # 删除 input 和 output
            EmissionSourceInputApplication.objects.filter(emission_source=application).delete()
            for item in data.inputs_outputs.inputs:
                EmissionSourceInputApplication.objects.create(emission_source=application, **item.model_dump())
            EmissionSourceOutputApplication.objects.filter(emission_source=application).delete()
            for item in data.inputs_outputs.outputs:
                EmissionSourceOutputApplication.objects.create(emission_source=application, **item.model_dump())
            return application

    @staticmethod
    def create_emission_source(user: User, data: EmissionSourceInSchema):
        emission_source = EmissionSource(
            creator=user,
            amount=1,
            uuid=uuid.uuid4(),
        )
        return DatabaseService.update_emission_source(emission_source, data)

    @staticmethod
    def update_emission_source(emission_source: EmissionSource, data: EmissionSourceInSchema):
        with transaction.atomic():
            if emission_source.id is not None:
                emission_source = EmissionSource.objects.select_for_update().get(pk=emission_source.id)

            for key, value in data.model_dump(exclude=["dataset", "management", "inputs_outputs"]).items():
                setattr(emission_source, key, value)
            emission_source.name = data.inputs_outputs.name.dict()
            emission_source.category_id = data.dataset.category_id
            emission_source.geography_id = data.dataset.geography_id
            emission_source.source = data.dataset.source.dict()
            emission_source.year = data.dataset.year
            emission_source.unit_id = data.inputs_outputs.unit_id
            emission_source.co2e = data.inputs_outputs.co2e
            emission_source.save()
            EmissionSourceManagement.objects.update_or_create(
                emission_source=emission_source, defaults=data.management.model_dump()
            )  # type: ignore
            EmissionSourceDataSet.objects.update_or_create(
                emission_source=emission_source, defaults=data.dataset.model_dump()
            )  # type: ignore
            # 删除 input 和 output
            EmissionSourceInput.objects.filter(emission_source=emission_source).delete()
            for item in data.inputs_outputs.inputs:
                EmissionSourceInput.objects.create(emission_source=emission_source, **item.model_dump())
            EmissionSourceOutput.objects.filter(emission_source=emission_source).delete()
            for item in data.inputs_outputs.outputs:
                EmissionSourceOutput.objects.create(emission_source=emission_source, **item.model_dump())
            return emission_source

    @staticmethod
    def reject_emission_source_application(application: EmissionSourceApplication, reason: str, user: User):
        with transaction.atomic():
            application = EmissionSourceApplication.objects.select_for_update().get(pk=application.id)
            if application.status != EMISSION_SOURCE_STATUS.ONGOING.value:
                raise HttpError(403, "当前状态不允许该操作")
            application.status = EMISSION_SOURCE_STATUS.REJECTED.value
            application.reason = reason
            application.approve_time = datetime.now()
            application.approver = user
            application.save()
            return application

    @staticmethod
    def accept_emission_source_application(application: EmissionSourceApplication, user: User):
        with transaction.atomic():
            application = EmissionSourceApplication.objects.select_for_update().get(pk=application.id)
            if application.status != EMISSION_SOURCE_STATUS.ONGOING.value:
                raise HttpError(403, "当前状态不允许该操作")
            application.status = EMISSION_SOURCE_STATUS.APPROVED.value
            application.approve_time = datetime.now()
            application.approver = user
            application.save()
            DatabaseService.create_emission_source_from_application(application)
            return application

    @staticmethod
    def create_emission_source_from_application(application: EmissionSourceApplication):
        emission_source = EmissionSource(
            creator=application.creator,
            amount=application.amount,
            uuid=application.uuid,
        )
        emission_source.name = application.name
        emission_source.category_id = application.category_id
        emission_source.geography_id = application.geography_id
        emission_source.source = application.source
        emission_source.year = application.year
        emission_source.unit_id = application.unit_id
        emission_source.co2e = application.co2e

        # dataset management input output
        emission_source.save()
        EmissionSourceDataSet.objects.create(
            emission_source=emission_source,
            name=application.dataset.name,
            alias=application.dataset.alias,
            year=application.dataset.year,
            amount=application.dataset.amount,
            functional_unit=application.dataset.functional_unit,
            specs=application.dataset.specs,
            boundary=application.dataset.boundary,
            category_id=application.dataset.category_id,
            technical_description=application.dataset.technical_description,
            usage=application.dataset.usage,
            flow_chart_id=application.dataset.flow_chart_id,
            allocation_principles=application.dataset.allocation_principles,
            model_description=application.dataset.model_description,
            data_treatment=application.dataset.data_treatment,
            source=application.dataset.source,
            geography_id=application.dataset.geography_id,
            unit_id=application.dataset.unit_id,
        )
        EmissionSourceManagement.objects.create(
            emission_source=emission_source, **model_to_dict(application.management, exclude=["id", "emission_source"])
        )
        for item in application.inputs.all():
            EmissionSourceInput.objects.create(
                emission_source=emission_source,
                flow_id=item.flow_id,
                flow_type=item.flow_type,
                amount=item.amount,
                unit_id=item.unit_id,
                related_emission_source_id=item.related_emission_source_id,
                description=item.description,
            )
        for item in application.outputs.all():
            EmissionSourceOutput.objects.create(
                emission_source=emission_source,
                flow_id=item.flow_id,
                flow_type=item.flow_type,
                amount=item.amount,
                unit_id=item.unit_id,
                description=item.description,
                type=item.type,
                allocation=item.allocation,
            )
        return emission_source

    @staticmethod
    def create_flow(data: FlowInSchema):
        flow = Flow()
        for key, value in data.model_dump().items():
            setattr(flow, key, value)
        flow.save()
        return flow

    @staticmethod
    def get_cockpit_data():
        nums = dict(
            total=200000,
            category=20,
            geography=200,
            user=2000,
            manufacturer=20,
            authority=200,
        )

        plateforms = []
        for row in Model.objects.filter(plateform=True).prefetch_related('life_cycles'):
            plateforms.append(
                dict(
                    id=row["id"],
                    name=row["name"],
                    co2e=row["co2e"],
                    flow_chart=row["flow_chart"],
                    life_cycles=[
                        dict(stage=x.stage, co2e=x.co2e) for x in row.life_cycles.all()
                    ],
                )
            )

        categories = []
        categories = [
            dict(name="制造业", count=1200),
            dict(name="采矿业", count=1100),
            dict(name="农、林、牧、渔业", count=1400),
            dict(name="电力、热力、燃气及水生产和供应业", count=320),
            dict(name="建筑业", count=123),
            dict(name="交通运输、仓储和邮政业", count=80),
        ]
        yearly = [
            dict(name="2019", count=100),
            dict(name="2020", count=120),
            dict(name="2021", count=140),
            dict(name="2022", count=160),
            dict(name="2023", count=180),
        ]
        hot_products = [
            dict(product_name="光伏组件", count=1000, percent=0.1),
            dict(product_name="动力电池", count=900, percent=0.09),
            dict(product_name="轻型电动汽车", count=800, percent=0.08),
            dict(product_name="通用硅酸盐水泥", count=700, percent=0.07),
        ]
        geographies = [
            dict(name="中国", count=1000, items=[
                dict(name="光伏组件", authority_name="方圆认证", count=1000),
                dict(name="动力电池", authority_name="方圆认证", count=900),
                dict(name="轻型电动汽车", authority_name="方圆认证", count=800),
                dict(name="通用硅酸盐水泥", authority_name="方圆认证", count=700),
            ]),
            # 以下地区的count随机100到1000
            dict(name="河北省", count=1000, items=[
                dict(name="光伏组件", authority_name="方圆认证", count=1000),
                dict(name="动力电池", authority_name="方圆认证", count=900),
                dict(name="轻型电动汽车", authority_name="方圆认证", count=800),
                dict(name="通用硅酸盐水泥", authority_name="方圆认证", count=700),
            ]),
            dict(name="山西省", count=1000, items=[
                dict(name="光伏组件", authority_name="方圆认证", count=1000),
                dict(name="动力电池", authority_name="方圆认证", count=900),
                dict(name="轻型电动汽车", authority_name="方圆认证", count=800),
                dict(name="通用硅酸盐水泥", authority_name="方圆认证", count=700),
            ]),
            dict(name="辽宁省", count=1000, items=[
                dict(name="光伏组件", authority_name="方圆认证", count=1000),
                dict(name="动力电池", authority_name="方圆认证", count=900),
                dict(name="轻型电动汽车", authority_name="方圆认证", count=800),
                dict(name="通用硅酸盐水泥", authority_name="方圆认证", count=700),
            ]),
            dict(name="吉林省", count=1000, items=[
                dict(name="光伏组件", authority_name="方圆认证", count=1000),
                dict(name="动力电池", authority_name="方圆认证", count=900),
                dict(name="轻型电动汽车", authority_name="方圆认证", count=800),
                dict(name="通用硅酸盐水泥", authority_name="方圆认证", count=700),
            ]),
            dict(name="黑龙江省", count=1000, items=[
                dict(name="光伏组件", authority_name="方圆认证", count=1000),
                dict(name="动力电池", authority_name="方圆认证", count=900),
                dict(name="轻型电动汽车", authority_name="方圆认证", count=800),
                dict(name="通用硅酸盐水泥", authority_name="方圆认证", count=700),
            ]),
        ]

        return dict(
            nums=nums,
            plateforms=plateforms,
            categories=categories,
            yearly=yearly,
            hot_products=hot_products,
            geographies=geographies,
        )


class DataBaseOperationLogService:
    """数据库操作日志"""

    @staticmethod
    def delete_emission_source_log(id: int):
        """删除本地因子"""
        name = ""
        return f"删除本地因子{name}"
