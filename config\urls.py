from django.conf import settings

# from django.conf.urls.i18n import i18n_patterns
from django.core.exceptions import ObjectDoesNotExist
from django.http import HttpRequest
from django.urls import path
from ninja import NinjaAPI
from ninja.errors import ValidationError

from lca.accounting.controllers import router as accounting_router
from lca.admin.resource_center.controllers import router as admin_resource_center_router
from lca.admin.user.controllers import router as admin_user_router
from lca.ai.controllers import router as ai_router
from lca.certification.authority_controllers import (
    router as authority_controllers_router,
)
from lca.certification.controllers import router as certification_router
from lca.certification.manufacturer_controllers import router as manufacturer_router
from lca.common.controllers import router as common_router
from lca.database.controllers import router as database_router
from lca.file.controller import router as file_router
from lca.resource_center.controllers import router as resource_center_router
from lca.users.controllers import router as user_router
from ztbory_django_shield.ninja import ShieldError, shield_error_handler, shield_router
from lca.admin.database.controller import router as admin_database_router
from lca.admin.accounting.controller import router as admin_accounting_router
from lca.admin.certification.controller import router as admin_certification_router
from lca.certification.goverment_controllers import router as goverment_router
from lca.admin.operation_log.controllers import router as operation_log_router
from lca.admin.cockpit.controller import router as cockpit_router


api = NinjaAPI(docs_url="/docs" if settings.DEBUG else None)
api.add_router("admin/cockpit", cockpit_router)
api.add_router("admin/goverment", goverment_router)
api.add_router("admin/accounting", admin_accounting_router)
api.add_router("admin/database", admin_database_router)
api.add_router("admin/user", admin_user_router)
api.add_router("admin/certification", admin_certification_router)
api.add_router("admin/operation-log", operation_log_router)
api.add_router("user", user_router)
api.add_router("/shield", shield_router)
api.add_router("ai", ai_router)
api.add_router("certification", certification_router)
api.add_router("certification/authority-admin", authority_controllers_router)
api.add_router("certification/manufacturer-admin", manufacturer_router)
api.add_router("accounting", accounting_router)
api.add_router("database", database_router)
api.add_router("common", common_router)
api.add_router("file", file_router)
api.add_router("admin/resource-center", admin_resource_center_router)
api.add_router("resource-center", resource_center_router)

api.add_exception_handler(ShieldError, shield_error_handler)


@api.exception_handler(ObjectDoesNotExist)
def object_does_not_exists_errors(request: HttpRequest, exc: ObjectDoesNotExist):
    return api.create_response(request, {"detail": "项目不存在", "message": f"{exc}"}, status=404)


@api.exception_handler(ValidationError)
def validation_errors(request: HttpRequest, exc: ValidationError):
    return api.create_response(request, {"detail": exc.errors}, status=422)


urlpatterns = [
    path("", api.urls),
]
# urlpatterns = i18n_patterns(
#     path('', api.urls),
#     prefix_default_language=True
# )
