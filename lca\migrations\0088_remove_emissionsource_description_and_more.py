# Generated by Django 5.2.1 on 2025-07-25 03:16

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0086_operationlog"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="emissionsource",
            name="description",
        ),
        migrations.RemoveField(
            model_name="emissionsource",
            name="type",
        ),
        migrations.RemoveField(
            model_name="emissionsource",
            name="update_time",
        ),
        migrations.AddField(
            model_name="emissionsource",
            name="creator",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                to=settings.AUTH_USER_MODEL,
                verbose_name="创建者",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="emissionsourcedataset",
            name="source",
            field=models.JSONField(blank=True, null=True, verbose_name="数据来源"),
        ),
        migrations.AddField(
            model_name="emissionsourceinput",
            name="flow_type",
            field=models.CharField(
                choices=[
                    ("elementary", "基本流"),
                    ("intermediate", "中间流"),
                    ("product", "产品流"),
                    ("co-product", "共产品流"),
                    ("waste", "废物流"),
                    ("resource", "资源流"),
                ],
                default="elementary",
                max_length=255,
                verbose_name="流类型",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="emissionsourceoutput",
            name="allocation",
            field=models.DecimalField(decimal_places=5, default=100, max_digits=20, verbose_name="分配占比"),
        ),
        migrations.AddField(
            model_name="emissionsourceoutput",
            name="flow_type",
            field=models.CharField(
                choices=[
                    ("elementary", "基本流"),
                    ("intermediate", "中间流"),
                    ("product", "产品流"),
                    ("co-product", "共产品流"),
                    ("waste", "废物流"),
                    ("resource", "资源流"),
                ],
                default="elementary",
                max_length=255,
                verbose_name="流类型",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="emissionsourceoutput",
            name="type",
            field=models.CharField(
                choices=[
                    ("main-product", "主产品"),
                    ("by-product", "共生产品"),
                    ("environmental-emission", "直接排放"),
                    ("waste", "待处置废弃物"),
                ],
                default="main-product",
                max_length=255,
                verbose_name="输出类型",
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="emissionsource",
            name="amount",
            field=models.DecimalField(decimal_places=5, default=1, max_digits=20, verbose_name="数量"),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="emissionsource",
            name="category",
            field=models.ForeignKey(
                default="C", on_delete=django.db.models.deletion.CASCADE, to="lca.category", verbose_name="类别"
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="emissionsource",
            name="geography",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="lca.geography", verbose_name="适用的地理区域"
            ),
        ),
        migrations.AlterField(
            model_name="emissionsource",
            name="name",
            field=models.JSONField(default=dict, verbose_name="名称"),
        ),
        migrations.AlterField(
            model_name="emissionsource",
            name="source",
            field=models.JSONField(max_length=255, verbose_name="数据来源"),
        ),
        migrations.AlterField(
            model_name="emissionsource",
            name="unit",
            field=models.ForeignKey(
                default="kg", on_delete=django.db.models.deletion.CASCADE, to="lca.unit", verbose_name="单位"
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="emissionsource",
            name="uuid",
            field=models.UUIDField(default=uuid.uuid4, editable=False, unique=True),
        ),
        migrations.AlterField(
            model_name="emissionsource",
            name="year",
            field=models.CharField(default=2025, max_length=255, verbose_name="时间代表性"),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="emissionsourcedataset",
            name="alias",
            field=models.JSONField(max_length=255, null=True, verbose_name="别名"),
        ),
        migrations.AlterField(
            model_name="emissionsourcedataset",
            name="allocation_principles",
            field=models.JSONField(blank=True, max_length=255, null=True, verbose_name="分配原则"),
        ),
        migrations.AlterField(
            model_name="emissionsourcedataset",
            name="data_treatment",
            field=models.JSONField(blank=True, max_length=255, null=True, verbose_name="数据处理"),
        ),
        migrations.AlterField(
            model_name="emissionsourcedataset",
            name="functional_unit",
            field=models.JSONField(max_length=100, verbose_name="功能单位"),
        ),
        migrations.AlterField(
            model_name="emissionsourcedataset",
            name="model_description",
            field=models.JSONField(blank=True, max_length=255, null=True, verbose_name="模型描述"),
        ),
        migrations.AlterField(
            model_name="emissionsourcedataset",
            name="name",
            field=models.JSONField(max_length=255, unique=True, verbose_name="名称"),
        ),
        migrations.AlterField(
            model_name="emissionsourcedataset",
            name="specs",
            field=models.JSONField(max_length=100, null=True, verbose_name="产品型号"),
        ),
        migrations.AlterField(
            model_name="emissionsourcedataset",
            name="technical_description",
            field=models.JSONField(max_length=255, null=True, verbose_name="技术描述"),
        ),
        migrations.AlterField(
            model_name="emissionsourcedataset",
            name="usage",
            field=models.JSONField(max_length=255, null=True, verbose_name="产品或工艺用途"),
        ),
        migrations.AlterField(
            model_name="emissionsourceinput",
            name="emission_source",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="inputs",
                to="lca.emissionsource",
                verbose_name="排放源",
            ),
        ),
        migrations.AlterField(
            model_name="emissionsourcemanagement",
            name="approve_contact",
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name="数据审核者联系方式"),
        ),
        migrations.AlterField(
            model_name="emissionsourcemanagement",
            name="approve_contact_detail",
            field=models.CharField(max_length=255, verbose_name="数据审核联系方式"),
        ),
        migrations.AlterField(
            model_name="emissionsourcemanagement",
            name="generate_contact",
            field=models.JSONField(verbose_name="数据生成联系人"),
        ),
        migrations.AlterField(
            model_name="emissionsourcemanagement",
            name="generate_contact_detail",
            field=models.CharField(max_length=255, verbose_name="数据生成联系方式"),
        ),
        migrations.AlterField(
            model_name="emissionsourcemanagement",
            name="generate_version",
            field=models.CharField(max_length=255, verbose_name="数据生成版本"),
        ),
        migrations.AlterField(
            model_name="emissionsourcemanagement",
            name="input_contact_detail",
            field=models.CharField(max_length=255, verbose_name="数据录入联系方式"),
        ),
        migrations.AlterField(
            model_name="emissionsourcemanagement",
            name="inputer_contact",
            field=models.JSONField(blank=True, null=True, verbose_name="数据录入者"),
        ),
        migrations.AlterField(
            model_name="emissionsourcemanagement",
            name="license_type",
            field=models.JSONField(null=True, verbose_name="许可类型"),
        ),
        migrations.AlterField(
            model_name="emissionsourcemanagement",
            name="owener",
            field=models.JSONField(blank=True, max_length=255, null=True, verbose_name="数据拥有者"),
        ),
        migrations.AlterField(
            model_name="emissionsourcemanagement",
            name="owener_version",
            field=models.CharField(max_length=255, verbose_name="数据拥有者版本"),
        ),
        migrations.AlterField(
            model_name="emissionsourcemanagement",
            name="publish",
            field=models.JSONField(null=True, verbose_name="发布信息"),
        ),
        migrations.AlterField(
            model_name="emissionsourcemanagement",
            name="view",
            field=models.JSONField(null=True, verbose_name="访问权限信息"),
        ),
        migrations.AlterField(
            model_name="emissionsourceoutput",
            name="emission_source",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="outputs",
                to="lca.emissionsource",
                verbose_name="排放源",
            ),
        ),
        migrations.AlterField(
            model_name="flow",
            name="name",
            field=models.JSONField(max_length=200, unique=True, verbose_name="名称"),
        ),
        migrations.AlterField(
            model_name="operationlog",
            name="operation_type",
            field=models.CharField(
                choices=[
                    ("CREATE", "新增"),
                    ("EDIT", "编辑"),
                    ("DELETE", "删除"),
                    ("LOGIN", "登录"),
                    ("LOGOUT", "登出"),
                    ("LOGOFF", "注销"),
                    ("SET_PERMISSIONS", "设置权限"),
                    ("IMPORT_DATABASE", "导入数据库"),
                    ("UPLOAD_FACTOR", "上传因子"),
                    ("REVIEW_FACTOR", "审核因子"),
                    ("SET_PLATFORM_MODEL", "设置平台模型"),
                    ("UNPUBLISH_INFO", "下架信息"),
                    ("CREATE_INFO", "新增了"),
                    ("PUBLISH_INFO", "发布了"),
                ],
                db_comment="操作类型",
                max_length=32,
                verbose_name="操作类型",
            ),
        ),
        migrations.CreateModel(
            name="Database",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ("name", models.CharField(max_length=255, unique=True, verbose_name="名称")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "待处理"),
                            ("in_progress", "进行中"),
                            ("success", "成功"),
                            ("failed", "失败"),
                        ],
                        max_length=255,
                        verbose_name="导入状态",
                    ),
                ),
                ("import_time", models.DateTimeField(blank=True, null=True, verbose_name="导入时间")),
                ("create_time", models.DateTimeField(auto_now_add=True, verbose_name="创建时间")),
                ("update_time", models.DateTimeField(auto_now=True, verbose_name="更新时间")),
                (
                    "file",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="lca.file",
                        verbose_name="文件",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="DatabaseImportTask",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "待处理"),
                            ("in_progress", "进行中"),
                            ("success", "成功"),
                            ("failed", "失败"),
                        ],
                        max_length=255,
                        verbose_name="导入状态",
                    ),
                ),
                ("progress", models.FloatField(default=0, verbose_name="导入进度")),
                ("import_time", models.DateTimeField(blank=True, null=True, verbose_name="导入时间")),
                ("create_time", models.DateTimeField(auto_now_add=True, verbose_name="创建时间")),
                ("update_time", models.DateTimeField(auto_now=True, verbose_name="更新时间")),
                (
                    "source",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="lca.database", verbose_name="数据库"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="EmissionSourceApplication",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ("co2e", models.DecimalField(decimal_places=5, default=0, max_digits=20, verbose_name="CO2e")),
                ("name", models.JSONField(default=dict, verbose_name="名称")),
                ("amount", models.DecimalField(decimal_places=5, max_digits=20, verbose_name="数量")),
                ("source", models.JSONField(max_length=255, verbose_name="数据来源")),
                ("year", models.CharField(max_length=255, verbose_name="时间代表性")),
                ("create_time", models.DateTimeField(auto_now_add=True, verbose_name="创建时间")),
                ("reason", models.TextField(blank=True, max_length=500, null=True, verbose_name="审核未通过的原因")),
                (
                    "status",
                    models.CharField(
                        choices=[("ongoing", "审核中"), ("approved", "通过"), ("rejected", "未通过")],
                        max_length=255,
                        verbose_name="状态",
                    ),
                ),
                ("approve_time", models.DateTimeField(blank=True, null=True, verbose_name="审核时间")),
                (
                    "approver",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="emission_source_approve_applications",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="审核者",
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="lca.category", verbose_name="类别"
                    ),
                ),
                (
                    "creator",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name="创建者"
                    ),
                ),
                (
                    "emission_source",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.emissionsource",
                        verbose_name="排放源",
                    ),
                ),
                (
                    "geography",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="lca.geography", verbose_name="适用的地理区域"
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="lca.unit", verbose_name="单位"),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="EmissionSourceDataSetApplication",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ("name", models.JSONField(verbose_name="名称")),
                ("alias", models.JSONField(max_length=255, null=True, verbose_name="别名")),
                ("year", models.CharField(max_length=255, verbose_name="时间代表性")),
                ("amount", models.DecimalField(decimal_places=5, max_digits=20, verbose_name="数量")),
                ("functional_unit", models.JSONField(max_length=100, verbose_name="功能单位")),
                ("specs", models.JSONField(max_length=100, null=True, verbose_name="产品型号")),
                (
                    "boundary",
                    models.CharField(
                        choices=[("cradle-gate", "从摇篮到大门"), ("cradle-grave", "从摇篮到坟墓")],
                        max_length=50,
                        verbose_name="系统边界",
                    ),
                ),
                ("technical_description", models.JSONField(max_length=255, null=True, verbose_name="技术描述")),
                ("usage", models.JSONField(max_length=255, null=True, verbose_name="产品或工艺用途")),
                (
                    "allocation_principles",
                    models.JSONField(blank=True, max_length=255, null=True, verbose_name="分配原则"),
                ),
                ("model_description", models.JSONField(blank=True, max_length=255, null=True, verbose_name="模型描述")),
                ("data_treatment", models.JSONField(blank=True, max_length=255, null=True, verbose_name="数据处理")),
                ("source", models.JSONField(blank=True, null=True, verbose_name="数据来源")),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="lca.category", verbose_name="类别"
                    ),
                ),
                (
                    "emission_source",
                    models.OneToOneField(
                        blank=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="dataset",
                        to="lca.emissionsourceapplication",
                        verbose_name="排放源",
                    ),
                ),
                (
                    "flow_chart",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="lca.file",
                        verbose_name="工艺流程图",
                    ),
                ),
                (
                    "geography",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="lca.geography", verbose_name="适用的地理区域"
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="lca.unit", verbose_name="单位"),
                ),
            ],
        ),
        migrations.CreateModel(
            name="EmissionSourceInputApplication",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("amount", models.DecimalField(decimal_places=5, max_digits=20, verbose_name="数量")),
                (
                    "flow_type",
                    models.CharField(
                        choices=[
                            ("elementary", "基本流"),
                            ("intermediate", "中间流"),
                            ("product", "产品流"),
                            ("co-product", "共产品流"),
                            ("waste", "废物流"),
                            ("resource", "资源流"),
                        ],
                        max_length=255,
                        verbose_name="流类型",
                    ),
                ),
                (
                    "emission_source",
                    models.ForeignKey(
                        blank=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="inputs",
                        to="lca.emissionsourceapplication",
                        verbose_name="排放源",
                    ),
                ),
                (
                    "flow",
                    models.ForeignKey(
                        null=True, on_delete=django.db.models.deletion.CASCADE, to="lca.flow", verbose_name="流"
                    ),
                ),
                (
                    "related_emission_source",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="related_emission_source_applications",
                        to="lca.emissionsource",
                        verbose_name="关联的排放源",
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="lca.unit", verbose_name="单位"),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="EmissionSourceManagementApplication",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("generate_version", models.CharField(max_length=255, verbose_name="数据生成版本")),
                ("generate_contact", models.JSONField(verbose_name="数据生成联系人")),
                ("generate_contact_detail", models.CharField(max_length=255, verbose_name="数据生成联系方式")),
                ("generate_create_time", models.DateTimeField(auto_now_add=True, verbose_name="数据生成时间")),
                ("generate_update_time", models.DateTimeField(auto_now=True, verbose_name="数据生成更新时间")),
                ("inputer_contact", models.JSONField(blank=True, null=True, verbose_name="数据录入者")),
                ("input_create_time", models.DateTimeField(auto_now_add=True, verbose_name="数据录入时间")),
                ("input_contact_detail", models.CharField(max_length=255, verbose_name="数据录入联系方式")),
                (
                    "approve_contact",
                    models.CharField(blank=True, max_length=255, null=True, verbose_name="数据审核者联系方式"),
                ),
                ("approve_contact_detail", models.CharField(max_length=255, verbose_name="数据审核联系方式")),
                ("approve_create_time", models.DateTimeField(auto_now_add=True, verbose_name="数据审核时间")),
                ("update_major_count", models.IntegerField(default=0, verbose_name="大修次数")),
                ("update_minor_count", models.IntegerField(default=0, verbose_name="小修次数")),
                ("publish", models.JSONField(null=True, verbose_name="发布信息")),
                ("view", models.JSONField(null=True, verbose_name="访问权限信息")),
                ("owener", models.JSONField(blank=True, max_length=255, null=True, verbose_name="数据拥有者")),
                ("owener_version", models.CharField(max_length=255, verbose_name="数据拥有者版本")),
                ("license_type", models.JSONField(null=True, verbose_name="许可类型")),
                ("copyright", models.CharField(max_length=255, null=True, verbose_name="版权信息")),
                (
                    "emission_source",
                    models.OneToOneField(
                        blank=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="management",
                        to="lca.emissionsourceapplication",
                        verbose_name="排放源",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="EmissionSourceOutputApplication",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "flow_type",
                    models.CharField(
                        choices=[
                            ("elementary", "基本流"),
                            ("intermediate", "中间流"),
                            ("product", "产品流"),
                            ("co-product", "共产品流"),
                            ("waste", "废物流"),
                            ("resource", "资源流"),
                        ],
                        max_length=255,
                        verbose_name="流类型",
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=5, max_digits=20, verbose_name="数量")),
                ("description", models.CharField(max_length=255, null=True, verbose_name="描述")),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("main-product", "主产品"),
                            ("by-product", "共生产品"),
                            ("environmental-emission", "直接排放"),
                            ("waste", "待处置废弃物"),
                        ],
                        max_length=255,
                        verbose_name="输出类型",
                    ),
                ),
                (
                    "allocation",
                    models.DecimalField(decimal_places=5, default=100, max_digits=20, verbose_name="分配占比"),
                ),
                (
                    "emission_source",
                    models.ForeignKey(
                        blank=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="outputs",
                        to="lca.emissionsourceapplication",
                        verbose_name="排放源",
                    ),
                ),
                (
                    "flow",
                    models.ForeignKey(
                        null=True, on_delete=django.db.models.deletion.CASCADE, to="lca.flow", verbose_name="流"
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="lca.unit", verbose_name="单位"),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
