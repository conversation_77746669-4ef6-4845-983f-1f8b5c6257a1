from datetime import datetime
from decimal import Decimal
from typing import Optional

from ninja import Schema
from pydantic import Field

from lca.accounting.models import (
    ALLOCATION_METHODS,
    BOUNDARIES,
    LIFE_CYCLE_STAGES,
    OUTPUT_TYPES,
    SOURCE_TYPES,
    INPUT_TYPES,
    Unit,
)
from lca.common.schema import UserSchema
from lca.file.schema import FileOut


class CN_EN(Schema):
    zh: str = Field(None, title="中文")
    en: str = Field(None, title="英文")


class GeographySchema(Schema):
    """
    类别
    """

    id: str = Field(..., title="ID")
    name: str = Field(..., title="地理名称")


class FlowSchema(Schema):
    """
    流
    """

    id: int = Field(..., title="ID")
    name: CN_EN = Field(..., title="名称")
    type: str = Field(..., title="流类型")


class CategorySchema(Schema):
    """
    类别
    """

    id: str = Field(..., title="ID")
    name: str = Field(..., title="类别名称")


class UnitSchema(Schema):
    """
    单位
    """

    id: str = Field(..., title="ID")


class EmissionSourceSchema(Schema):
    """
    排放源
    """

    id: int = Field(..., title="ID")
    name: CN_EN = Field(..., title="名称")
    amount: float = Field(..., title="数量")
    unit: UnitSchema = Field(..., title="单位")
    geography: GeographySchema = Field(..., title="地理")
    category: CategorySchema = Field(..., title="分类")
    source: CN_EN = Field(..., title="来源")
    year: int = Field(..., title="年份")
    creator: UserSchema = Field(..., title="提交人")
    approver: Optional[UserSchema] = Field(None, title="审核者")
    create_time: datetime = Field(..., title="创建时间")
    approve_time: Optional[datetime] = Field(None, title="审核时间")


class ModelInSchema(Schema):
    """
    模型
    """

    name: str = Field(..., title="产品名称", max_length=100)
    functional_unit: str = Field(..., title="功能单位", max_length=100)
    specs: str = Field(..., title="型号")
    category_id: str = Field(..., title="类别")
    company_name: str = Field(None, title="公司名称", max_length=100)
    description: str = Field(None, title="描述", max_length=200)
    boundary: BOUNDARIES = Field(..., title="系统边界")
    year: int = Field(..., title="年份")
    geography_id: str = Field(..., title="产品产地")
    rule: str = Field(None, title="取舍规则")
    amount: float = Field(..., title="产量")
    unit_id: str = Field(..., title="单位")
    image_id: str = Field(None, title="图片地址")
    production_report_ids: list[str] = Field(..., title="《工业产销总值及主要产品产量》或《生产统计产量表》")
    bom_ids: list[str] = Field(..., title="BOM表")
    purchase_contract_ids: list[str] = Field(..., title="采购合同")
    water_bill_ids: list[str] = Field(..., title="水费结算单")
    energy_report_ids: list[str] = Field(..., title="《能源购进、消费与库存表》")
    energy_tax_ids: list[str] = Field(..., title="能源购买发票")
    energy_other_ids: list[str] = Field(None, title="能源其他凭证")
    environment_impact_report_ids: list[str] = Field(None, title="环评报告")
    environment_check_report_ids: list[str] = Field(None, title="环境检测报告")
    outlet_flow_report_ids: list[str] = Field(None, title="排水口检测流量统计表")
    waste_ledger_ids: list[str] = Field(None, title="废弃物台账")
    craft_process_ids: list[str] = Field(None, title="工艺流程图")


class BaseModelSchema(Schema):
    """
    基础模型列表
    """

    id: int = Field(..., title="ID")
    product: str | None = Field(None, title="产品")
    name: str = Field(..., title="产品名称")
    functional_unit: str = Field(..., title="功能单位")
    category: CategorySchema = Field(..., title="类别ID")
    specs: str = Field(..., title="型号")
    company_name: str | None = Field(..., title="公司名称")
    description: str | None = Field(..., title="描述")
    boundary: BOUNDARIES = Field(..., title=f"系统边界，可选值{BOUNDARIES.choices}")
    year: int = Field(..., title="年份")
    geography: GeographySchema = Field(..., title="产品产地")
    rule: str | None = Field(None, title="取舍规则")
    amount: float = Field(..., title="产量")
    unit: str = Field(..., title="单位", alias="unit_id")
    report: Optional[FileOut] = Field(None, title="报告")
    co2e: Decimal = Field(..., title="二氧化碳当量")
    creator: UserSchema = Field(..., title="创建者")
    image: Optional[FileOut] = Field(None, title="图片")
    update_time: datetime = Field(..., title="更新时间")


class ProcessSchema(Schema):
    """
    单元过程
    """

    id: int = Field(..., title="ID")
    name: str = Field(..., title="名称")
    allocation_method: ALLOCATION_METHODS | None = Field(None, title="分配方法")
    inputs: list["InputSchema"] = Field(..., title="输入")
    outputs: list["OutputSchema"] = Field(..., title="输出")
    co2e: Decimal = Field(..., title="二氧化碳当量")


class ProcessInSchema(Schema):
    name: str = Field(..., title="名称")


class TransportSchema(Schema):
    gross_weight: Decimal = Field(..., title="毛重")
    origin: str | None = Field(None, title="起点")
    terminal: str | None = Field(None, title="终点")
    emission_source: "EmissionSourceSchema" = Field(..., title="排放源")
    distance: Decimal = Field(..., title="距离")


class TransportInSchema(Schema):
    gross_weight: Decimal = Field(..., title="毛重")
    origin: str | None = Field(None, title="起点")
    terminal: str | None = Field(None, title="终点")
    emission_source_id: int = Field(..., title="排放源")
    distance: Decimal = Field(..., title="距离")


class FileSchema(Schema):
    """
    文件
    """

    name: str = Field(..., title="名称")
    path: str = Field(..., title="存储地址")
    url: str = Field(..., title="访问地址")


class FileInSchema(Schema):
    """
    文件
    """

    name: str = Field(..., title="名称")
    path: str = Field(..., title="存储地址")


class GuessInfoInSchema(Schema):
    """
    猜测信息
    """

    name: str = Field(..., title="名称")


class GuessInfoSchema(Schema):
    """
    猜测信息
    """

    name: str = Field(..., title="名称")
    category: CategorySchema = Field(..., title="类别")
    functional_unit: str = Field(..., title="功能单位")


class CustomFactorSchema(Schema):
    """
    自定义因子
    """

    source: str = Field(..., title="来源")
    amount: Decimal = Field(..., title="数量")
    unit: str = Field(..., title="单位", alias="unit_id")
    description: str | None = Field(None, title="描述")
    co2e: Decimal = Field(..., title="二氧化碳当量")


class CustomFactorInSchema(Schema):
    """
    自定义因子
    """

    source: str = Field(..., title="来源")
    # amount: Decimal = Field(..., title="数量")
    unit: str = Field(..., title="单位")
    co2e: Decimal = Field(..., title="二氧化碳当量")
    description: str | None = Field(None, title="描述")


class LifeCycleSchema(Schema):
    """
    生命周期
    """

    id: int = Field(..., title="ID")
    stage: LIFE_CYCLE_STAGES = Field(..., title="阶段")
    process: ProcessSchema | None = Field(None, title="输入过程")
    co2e: Decimal = Field(..., title="二氧化碳当量")


class InputSchema(Schema):
    """
    输入
    """

    id: int = Field(None, title="ID")
    name: str = Field(..., title="名称")
    type: INPUT_TYPES = Field(..., title="类型")
    amount: Decimal = Field(..., title="数量")
    unit: str = Field(..., title="单位", alias="unit_id")
    source_type: SOURCE_TYPES = Field(..., title="数据来源")
    emission_source: Optional["EmissionSourceSchema"] | None = Field(None, title="排放源")
    process: ProcessSchema | None = Field(None, title="过程")
    custom_factor: CustomFactorSchema | None = Field(None, title="自定义因子")
    transports: list[TransportSchema] = Field([], title="运输")
    co2e: Decimal = Field(..., title="CO2e")


class InputInSchema(Schema):
    name: str = Field(..., title="名称")
    type: INPUT_TYPES = Field(..., title="类型")
    amount: Decimal = Field(..., title="数量")
    unit: str = Field(..., title="单位", alias="unit_id")
    source_type: SOURCE_TYPES = Field(..., title="数据来源")
    emission_source_id: int | None = Field(None, title="排放源ID")
    custom_factor: CustomFactorInSchema | None = Field(None, title="自定义因子")
    transports: list[TransportInSchema] = Field([], title="运输信息")


class AllocationSchema(Schema):
    amount: Decimal | None = Field(None, title="数量")
    unit: str | None = Field(None, title="单位", alias="unit_id")
    percent: Decimal | None = Field(None, title="百分比")
    description: str | None = Field(None, title="描述")
    emission_source: Optional[EmissionSourceSchema] | None = Field(None, title="排放源")
    custom_factor: CustomFactorSchema | None = Field(None, title="自定义因子")


class AllocationInSchema(Schema):
    method: ALLOCATION_METHODS | None = Field(None, title="分配方法")
    percent: Decimal | None = Field(None, title="百分比")
    amount: Decimal | None = Field(None, title="替代数量")
    unit: str | None = Field(None, title="单位", alias="unit_id")
    description: str | None = Field(None, title="描述")
    emission_source_id: int | None = Field(None, title="排放源")
    custom_factor: CustomFactorInSchema | None = Field(None, title="自定义因子")


class OutputSchema(Schema):
    id: int = Field(..., title="id")
    name: str | None = Field(None, title="名称")
    flow: FlowSchema | None = Field(None, title="流")
    type: OUTPUT_TYPES = Field(..., title="类型")
    amount: Decimal = Field(..., title="数量")
    unit: str = Field(..., title="单位", alias="unit_id")
    allocation: AllocationSchema | None = Field(None, title="分配")
    emission_source: Optional[EmissionSourceSchema] = Field(None, title="回收排放源")
    custom_factor: CustomFactorSchema | None = Field(None, title="自定义因子")


class OutputInSchema(Schema):
    name: str = Field(..., title="名称")
    flow_id: int | None = Field(None, title="流")
    type: OUTPUT_TYPES = Field(..., title="类型")
    amount: Decimal = Field(..., title="数量")
    unit_id: str = Field(..., title="单位")
    allocation: AllocationInSchema = Field(None, title="分配")
    emission_source_id: int | None = Field(None, title="关联因子")
    custom_factor: CustomFactorInSchema = Field(None, title="自定义因子")


class CalculaterSchema(Schema):
    """
    计算器
    """

    id: int = Field(..., title="id")
    name: str = Field(..., title="名称")


class ParamSchema(Schema):
    """
    参数
    """

    name: str = Field(..., title="名称")
    type: str = Field(..., title="类型")
    default: str | float | None = Field(None, title="默认值")
    description: str = Field(..., title="描述")


class FormulaSchema(Schema):
    """
    公式
    """

    id: int = Field(..., title="id")
    name: str = Field(..., title="名称")
    params: list[ParamSchema] = Field(..., title="参数")


class CalculaterDetailSchema(Schema):
    """
    计算器详情
    """

    id: int = Field(..., title="id")
    name: str = Field(..., title="名称")
    formulas: list[FormulaSchema] = Field(..., title="公式")


class ParamInSchema(Schema):
    name: str = Field(..., title="参数名称")
    value: str | float = Field(..., title="参数值")


class FormulaInSchema(Schema):
    id: int = Field(..., title="公式 id")
    params: list[ParamInSchema] = Field(..., title="参数列表")


class CalculaterInSchema(Schema):
    process_id: int = Field(..., title="过程 id")
    formulas: list[FormulaInSchema] = Field(..., title="公式列表")


class ModelSchema(BaseModelSchema):
    """
    模型
    """

    image: Optional[FileOut] = Field(None, title="图片")
    report: Optional[FileOut] = Field(None, title="报告")
    analysis_report: Optional[FileOut] = Field(None, title="分析报告")
    certification_report: Optional[FileOut] = Field(None, title="认证报告")
    production_reports: list[FileOut] = Field(..., title="《工业产销总值及主要产品产量》或《生产统计产量表》")
    boms: list[FileOut] = Field(..., title="BOM表")
    purchase_contracts: list[FileOut] = Field(..., title="采购合同")
    water_bills: list[FileOut] = Field(..., title="水费结算单")
    energy_reports: list[FileOut] = Field(..., title="《能源购进、消费与库存表》")
    energy_taxs: list[FileOut] = Field(..., title="能源购买发票")
    energy_others: list[FileOut] = Field(..., title="能源其他凭证")
    environment_impact_reports: list[FileOut] = Field(..., title="环评报告")
    environment_check_reports: list[FileOut] = Field(..., title="环境检测报告")
    outlet_flow_reports: list[FileOut] = Field(..., title="排水口检测流量统计表")
    waste_ledgers: list[FileOut] = Field(..., title="废物台账")
    craft_processes: list[FileOut] = Field(..., title="工艺流程图")


class FactorSchema(Schema):
    """通用因子数据"""

    co2e: Decimal = Field(..., title="CO2e")
    amount: Decimal = Field(..., title="数量")
    unit: Unit = Field(..., title="单位")

    class Config:
        arbitrary_types_allowed = True


class Co2eEachStageSchema(Schema):
    stage: str = Field(..., title="阶段")
    co2e: Decimal = Field(..., title="二氧化碳当量")


class Co2eEachItemListSchema(Schema):
    name: str = Field(..., title="清单名称")
    co2e: Decimal = Field(..., title="二氧化碳当量")


class Co2eEachItemSchema(Schema):
    item_name: str = Field(..., title="清单名称")
    co2e_list: list[Co2eEachItemListSchema] = Field(..., title="二氧化碳当量")


class LevelInfoSchema(Schema):
    """
    等级信息
    """

    limit: Decimal = Field(..., title="限值")
    top: Decimal = Field(..., title="领跑值")
    percent: Decimal = Field(..., title="领先行业百分比")
    unit_id: str = Field(..., title="单位")


class CompareItemSchema(Schema):
    name: str = Field(..., title="名称")
    Geography: GeographySchema = Field(None, title="地理")
    amount: Decimal = Field(..., title="数量")
    unit_id: str = Field(..., title="单位")


class CompareSchema(Schema):
    name: str = Field(..., title="名称")
    current: CompareItemSchema = Field(..., title="当前值")
    advise: CompareItemSchema = Field(..., title="建议值")


class LcaOutputSchema(Schema):
    id: int = Field(..., title="ID")
    name: str = Field(..., title="产品名称")
    category_name: str = Field(..., title="产品类别")
    boundary_name: str = Field(..., title="系统边界名称")

    amount: Decimal = Field(..., title="产量")
    unit_name: str = Field(..., title="产量单位名称")

    co2e: Decimal | None = Field(None, title="产品碳足迹")
    has_generated_report: bool = Field(None, title="是否已生成报告")
    image: Optional[FileOut] = Field(None, title="产品图片")
    report: Optional[FileOut] = Field(None, title="产品报告")
    co2e_each_stage: list[Co2eEachStageSchema] | None = Field(None, title="产品按阶段碳足迹")
    level: LevelInfoSchema = Field(None, title="等级信息")
    input_compare: list[CompareSchema] = Field(None, title="原材料对比")
