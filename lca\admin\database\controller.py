from ninja import Query, Router
from infra.PageNumberPagination import PageNumberPagination
from infra.decorators import permission_required
from ninja.pagination import paginate
from infra.filter import filter
from lca.accounting.models import EmissionSource
from lca.accounting.schema import EmissionSourceSchema, FlowSchema
from lca.database.models import (
    DATABASE_IMPORT_STATUS,
    EMISSION_SOURCE_STATUS,
    FLOW_TYPES,
    Database,
    DatabaseImportTask,
    EmissionSourceApplication,
    Flow,
)
from lca.database.schema import (
    DatabaseImportTaskSchema,
    DatabaseInSchema,
    DatabaseSchema,
    EmissionSourceApplicationRowSchema,
    EmissionSourceApplicationSchema,
    EmissionSourceDetailAdminSchema,
    EmissionSourceInSchema,
    EmissionSourceRejectInSchema,
    EmissionSourceDetailSchema,
    FlowInSchema,
)
from lca.database.services import DataBaseOperationLogService
from lca.database.services import DatabaseService
from lca.file.models import File
from lca.file.schema import FileOut
from lca.operation_log.decorators import log_operation
from lca.operation_log.models import OperationType


router = Router(tags=["后台-数据库"])


@router.get("/", summary="获取数据库列表", response=list[DatabaseSchema], operation_id="adminGetDatabaseList")
@paginate(PageNumberPagination)
@permission_required("SystemDatabase")
def get_database_list(
    request,
    name: str = Query(None, title="名称"),
):
    return filter(
        Database.objects.order_by("-create_time"),
        status=DATABASE_IMPORT_STATUS.SUCCESS,
        name__icontains=name,
    )


@router.post("/", summary="创建数据库", response=DatabaseImportTaskSchema, operation_id="adminCreateDatabase")
@permission_required("SystemDatabase")
@log_operation(OperationType.CREATE, main_content="数据库", content_field="name")
def create_database(request, data: DatabaseInSchema):
    file = File.objects.get(pk=data.file_id)
    return DatabaseService.import_database(data.name, file)


@router.post("/{id}/export", summary="导出数据库", response=FileOut, operation_id="adminExportDatabase")
@permission_required("SystemDatabase")
@log_operation(OperationType.EXPORT_DATABASE, main_content="数据库", content_field="file_name")
def export_database(request, id: int):
    database = Database.objects.get(pk=id)
    return DatabaseService.export_database(database)


@router.get(
    "/task",
    summary="获取导入任务列表",
    response=list[DatabaseImportTaskSchema],
    operation_id="adminGetDatabaseTaskList",
)
@paginate(PageNumberPagination)
@permission_required("SystemDatabase")
def get_database_task_list(request):
    return filter(
        DatabaseImportTask.objects.order_by("-create_time"),
    )


@router.get(
    "emission-source",
    summary="获取本地因子列表",
    response=list[EmissionSourceSchema],
    operation_id="adminGetEmissionSourceList",
)
@paginate(PageNumberPagination)
@permission_required("SystemDatabase")
def get_emission_source_list(
    request,
    name: str = Query(None, title="名称"),
    year: int = Query(None, title="年份"),
    geography_id: str = Query(None, title="地理ID"),
):
    return filter(
        EmissionSource.objects.order_by("-create_time"),
        name__icontains=name,
        year=year,
        geography_id=geography_id,
        # status=EMISSION_SOURCE_STATUS.APPROVED,
    )


@router.get(
    "emission-source/{id}",
    summary="获取本地因子详情",
    response=EmissionSourceDetailSchema,
    operation_id="adminGetEmissionSource",
)
@permission_required("SystemDatabase")
def get_emission_source(request, id: int):
    return EmissionSource.objects.get(pk=id)


@router.post(
    "emission-source",
    summary="创建本地因子",
    response=EmissionSourceSchema,
    operation_id="adminCreateEmissionSource",
)
@permission_required("SystemDatabase")
@log_operation(OperationType.CREATE, main_content="本地因子", content_field="inputs_outputs.name.zh")
def create_emission_source(request, data: EmissionSourceInSchema):
    return DatabaseService.create_emission_source(request.user, data)


@router.post("emission-source/{id}/delete", summary="删除本地因子", operation_id="adminDeleteEmissionSource")
@permission_required("SystemDatabase")
@log_operation(
    OperationType.DELETE, operation_func=DataBaseOperationLogService.delete_emission_source_log, run_before_view=True
)  # todo 不知道哪个表里面的字段
def delete_emission_source(request, id: int):
    EmissionSource.delete(id=id)
    return True


@router.post(
    "emission-source/{id}/update",
    summary="更新本地因子",
    response=EmissionSourceDetailSchema,
    operation_id="adminUpdateEmissionSource",
)
@permission_required("SystemDatabase")
@log_operation(OperationType.EDIT, main_content="本地因子", content_field="inputs_outputs.name.zh")
def update_emission_source(request, id: int, data: EmissionSourceInSchema):
    emission_source = EmissionSource.objects.get(pk=id)
    return DatabaseService.update_emission_source(emission_source, data)


@router.get(
    "approval",
    summary="获取待审批因子列表",
    response=list[EmissionSourceApplicationRowSchema],
    operation_id="adminGetApprovalEmissionSourceList",
)
@paginate(PageNumberPagination)
@permission_required("SystemDatabase")
def get_approval_emission_source_list(
    request,
    name: str = Query(None, title="名称"),
    year: int = Query(None, title="年份"),
    geography_id: str = Query(None, title="地理ID"),
):
    return filter(
        EmissionSourceApplication.objects.order_by("-create_time"),
        name__icontains=name,
        year=year,
        geography_id=geography_id,
        status=EMISSION_SOURCE_STATUS.ONGOING,
    )


@router.get(
    "approval/logs",
    summary="获取审批因子日志列表",
    response=list[EmissionSourceApplicationRowSchema],
    operation_id="adminGetApprovalEmissionSourceLogs",
)
@paginate(PageNumberPagination)
@permission_required("SystemDatabase")
def get_approval_emission_source_logs(
    request,
    name: str = Query(None, title="名称"),
    year: int = Query(None, title="年份"),
    geography_id: str = Query(None, title="地理ID"),
):
    return filter(
        EmissionSourceApplication.objects.order_by("-create_time"),
        name__icontains=name,
        year=year,
        geography_id=geography_id,
        status__in=[EMISSION_SOURCE_STATUS.APPROVED, EMISSION_SOURCE_STATUS.REJECTED],
    )


@router.get(
    "approval/{id}",
    summary="获取审批因子详情",
    response=EmissionSourceApplicationSchema,
    operation_id="adminGetApprovalEmissionSourceDetail",
)
@permission_required("SystemDatabase")
def get_approval_emission_source_detail(request, id: int):
    return EmissionSourceApplication.objects.get(pk=id)


@router.post(
    "approval/{id}/accept",
    summary="通过审批因子",
    response=EmissionSourceDetailAdminSchema,
    operation_id="adminAcceptApprovalEmissionSource",
)
@permission_required("SystemDatabase")
@log_operation(
    OperationType.REVIEW_ACCEPT, main_content="本地因子", content_field="inputs_outputs.name.zh"
)  # todo 未找到返回的因子名称
def accept_approval_emission_source(request, id: int):
    application = EmissionSourceApplication.objects.get(pk=id)
    return DatabaseService.accept_emission_source_application(application, request.user)


@router.post(
    "approval/{id}/reject",
    summary="拒绝审批因子",
    response=EmissionSourceDetailAdminSchema,
    operation_id="adminRejectApprovalEmissionSource",
)
@permission_required("SystemDatabase")
@log_operation(
    OperationType.REVIEW_REJECT, main_content="本地因子", content_field="inputs_outputs.name.zh"
)  # todo 未找到返回的因子名称
def reject_approval_emission_source(request, id: int, data: EmissionSourceRejectInSchema):
    application = EmissionSourceApplication.objects.get(pk=id)
    return DatabaseService.reject_emission_source_application(application, data.reason, request.user)


@router.post("flow", summary="创建流", response=FlowSchema, operation_id="adminCreateFlow")
@permission_required("SystemDatabase")
@log_operation(OperationType.CREATE, main_content="流", content_field="name")
def create_flow(request, data: FlowInSchema):
    return DatabaseService.create_flow(data)


@router.get("flow", summary="获取流列表", response=list[FlowSchema], operation_id="adminGetFlowList")
@paginate(PageNumberPagination)
@permission_required("SystemDatabase")
def get_flow_list(request, type: FLOW_TYPES = Query(None, title="流类型"), name: str = Query(None, title="名称")):
    return filter(
        Flow.objects.order_by("-id"),
        type=type,
        name__icontains=name,
    )
