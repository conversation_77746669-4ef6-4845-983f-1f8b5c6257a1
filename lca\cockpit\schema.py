from decimal import Decimal
from typing import Optional
from ninja import Schema
from pydantic import Field

from lca.file.schema import FileOut


class CockpitItemSchema(Schema):
    name: str = Field(..., title="类别")
    count: int = Field(..., title="数量")


class CockpitNumSchema(Schema):
    total: int = Field(..., title="因子总数")
    category: int = Field(..., title="因子覆盖行业数")
    geography: int = Field(..., title="因子涵盖地区数")
    user: int = Field(..., title="用户总量")
    manufacturer: int = Field(..., title="企业用户总量")
    authority: int = Field(..., title="认证机构用户总量")


class CockpitCo2eEachStageSchema(Schema):
    stage: str = Field(..., title="阶段")
    co2e: Decimal = Field(..., title="二氧化碳当量")


class CockpitItem(Schema):
    name: str = Field(..., title="名称")
    co2e: Decimal = Field(..., title="碳足迹")


class CockpitPlateformItem(Schema):
    name: str = Field(..., title="名称")
    id: int = Field(..., title="对应模型的 id")
    co2e: Decimal = Field(..., title="碳足迹")
    processes: list[CockpitItem] = Field(..., title="单元过程碳足迹分析")
    life_cycles: list[CockpitCo2eEachStageSchema]
    flow_chart: Optional[FileOut] = Field(None, title="工艺流程图")


class CockpitLCA(Schema):
    co2e: Decimal = Field(..., title="碳足迹")
    life_cycles: list[CockpitItem]
    processes: list[CockpitItem]


class CockpitCertificationItem(Schema):
    name: str = Field(..., title="产品")
    authority_name: str = Field(..., title="认证机构")
    count: int = Field(..., title="数量")


class CockpitGeographyDataSchema(Schema):
    name: str = Field(..., title="地理区域")
    count: int = Field(..., title="数量")
    items: list[CockpitCertificationItem] = Field(..., title="认证产品")


class CockpitHotProductSchema(Schema):
    product_name: str = Field(..., title="产品")
    count: int = Field(..., title="数量")
    percent: float = Field(..., title="占比")


class CockpitData(Schema):
    nums: CockpitNumSchema = Field(..., title="数量")
    plateforms: list[CockpitPlateformItem] = Field(..., title="平台模型产品")
    categories: list[CockpitItemSchema] = Field(..., title="碳足迹因子按行业分类柱状图")
    yearly: list[CockpitItemSchema] = Field(..., title="年度数据收录增长趋势")
    hot_products: list[CockpitHotProductSchema] = Field(..., title="热门产品")
    geographies: list[CockpitGeographyDataSchema] = Field(..., title="地图按照地区展示各地区通过认证的数量")
